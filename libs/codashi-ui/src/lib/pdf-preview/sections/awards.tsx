import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  awards: NonNullable<Resume['awards']>;
};

export const PdfResumeAwards: FC<Props> = ({ awards }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
        flexWrap: 'wrap',
      }}
    >
      <SectionTitle>Awards</SectionTitle>

      {awards.map((award, idx) => (
        <View
          key={idx}
          style={{
            gap: 6,
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 4,
              width: '100%',
            }}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.md,
                  fontWeight: theme.typography.fontWeights.bold,
                  color: theme.colors.text.primary,
                }}
              >
                {award.title}
              </Text>
              {award.date && (
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.xs,
                    color: theme.colors.text.subtle,
                  }}
                >
                  {new Date(award.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                  })}
                </Text>
              )}
            </View>
            {award.awarder && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  color: theme.colors.text.subtle,
                  fontStyle: 'italic',
                }}
              >
                {award.awarder}
              </Text>
            )}
          </View>
          {award.summary && (
            <Text
              style={{
                fontSize: theme.typography.fontSizes.xs,
                color: theme.colors.text.primary,
                marginTop: 4,
              }}
            >
              {award.summary}
            </Text>
          )}
        </View>
      ))}
    </View>
  );
};
