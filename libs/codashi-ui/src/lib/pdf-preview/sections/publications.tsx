import { Link, Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  publications: NonNullable<Resume['publications']>;
};

export const PdfResumePublications: FC<Props> = ({ publications }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
      }}
    >
      <SectionTitle>Publications</SectionTitle>

      {publications.map((publication, idx) => (
        <View
          key={idx}
          style={{
            gap: 6,
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Text
              style={{
                fontSize: theme.typography.fontSizes.md,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {publication.name}
            </Text>
            {publication.release_date && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.xs,
                  color: theme.colors.text.subtle,
                }}
              >
                {new Date(publication.release_date).toLocaleDateString(
                  'en-US',
                  {
                    year: 'numeric',
                    month: 'short',
                  }
                )}
              </Text>
            )}
          </View>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
            }}
          >
            {publication.publisher && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  color: theme.colors.text.subtle,
                  fontStyle: 'italic',
                }}
              >
                {publication.publisher}
              </Text>
            )}
            {publication.url && (
              <Link
                href={publication.url}
                style={{ fontSize: theme.typography.fontSizes.sm }}
              >
                [View Publication]
              </Link>
            )}
          </View>
          {publication.summary && (
            <Text
              style={{
                fontSize: theme.typography.fontSizes.xs,
                color: theme.colors.text.primary,
                marginTop: 4,
              }}
            >
              {publication.summary}
            </Text>
          )}
        </View>
      ))}
    </View>
  );
};
