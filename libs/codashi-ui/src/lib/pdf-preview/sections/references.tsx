import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  references: NonNullable<Resume['references']>;
};

export const PdfResumeReferences: FC<Props> = ({ references }) => {
  if (!references.length) return null;
  return (
    <View
      style={{
        gap: 12,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
      }}
    >
      <SectionTitle>References</SectionTitle>
      <View style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
        {references.map((ref, idx) => (
          <View key={`${ref.name}-${idx}`} style={{ gap: 2 }}>
            <Text
              style={{
                fontSize: theme.typography.fontSizes.sm,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {ref.name}
              {ref.position && `, ${ref.position}`}
              {ref.company && ` (${ref.company})`}
            </Text>
            {ref.reference && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  fontStyle: 'italic',
                }}
              >
                "{ref.reference}"
              </Text>
            )}
            {(ref.email || ref.phone) && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.xs,
                  color: theme.colors.text.subtle,
                }}
              >
                {ref.email && ref.email}
                {ref.email && ref.phone && ' | '}
                {ref.phone && ref.phone}
              </Text>
            )}
          </View>
        ))}
      </View>
    </View>
  );
};
