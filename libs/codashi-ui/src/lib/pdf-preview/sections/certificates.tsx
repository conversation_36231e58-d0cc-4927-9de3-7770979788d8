import { Link, Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  certificates: NonNullable<Resume['certificates']>;
};

export const PdfResumeCertificates: FC<Props> = ({ certificates }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
      }}
    >
      <SectionTitle>Certificates</SectionTitle>

      {certificates.map((cert, idx) => (
        <View
          key={idx}
          style={{
            gap: 6,
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <Text
              style={{
                fontSize: theme.typography.fontSizes.md,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {cert.name}
            </Text>
            {cert.date && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.xs,
                  color: theme.colors.text.subtle,
                }}
              >
                {new Date(cert.date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                })}
              </Text>
            )}
          </View>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
            }}
          >
            {cert.issuer && (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  color: theme.colors.text.subtle,
                  fontStyle: 'italic',
                }}
              >
                {cert.issuer}
              </Text>
            )}
            {cert.url && (
              <Link
                href={cert.url}
                style={{ fontSize: theme.typography.fontSizes.sm }}
              >
                [Verify]
              </Link>
            )}
          </View>
        </View>
      ))}
    </View>
  );
};
