import { Text, View } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import React from 'react';

import { type Resume } from '@awe/codashi-core';
import { theme } from '../theme';

type Props = {
  section: NonNullable<Resume['header']>;
};

export const PdfResumeHeader: React.FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        padding: 10,
        display: 'flex',
        flexDirection: 'row',
        gap: 6,
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {section.items.map((item, index) => {
        return exhaustive(item.name, {
          name: () => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.lg,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {item.value as string}
            </Text>
          ),
          title: () => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value as string}
            </Text>
          ),
          email: () => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
                color: theme.colors.text.link,
              }}
            >
              {item.value as string}
            </Text>
          ),
          phone: () => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value as string | null}
            </Text>
          ),
          url: () => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value as string | null}
            </Text>
          ),
          location: () => {
            const location = item.value as NonNullable<
              Resume['header']['items'][number] & { name: 'location' }
            >['value'];

            if (!location) {
              return null;
            }

            return (
              <Text
                key={`${item.name}-${index}`}
                style={{
                  fontSize: 12,
                }}
              >
                {[
                  location.address,
                  location.city,
                  location.region,
                  location.country_code,
                  location.postal_code,
                ]
                  .filter(Boolean)
                  .join(', ')}
              </Text>
            );
          },
          profile: () => {
            const profile = item.value as NonNullable<
              Resume['header']['items'][number] & { name: 'profile' }
            >['value'];
            if (!profile) {
              return null;
            }

            return (
              <View key={`${item.name}-${index}`} style={{ marginTop: 8 }}>
                <Text style={{ fontSize: 12 }} key={`${item.name}-${index}`}>
                  {profile.network ? `${profile.network}: ` : ''}
                  {profile.username || ''}
                  {profile.url ? ` (${profile.url})` : ''}
                </Text>
              </View>
            );
          },
          summary: () => {
            const summary = item.value as string | null;
            return (
              <Text
                key={`${item.name}-${index}`}
                style={{
                  fontSize: theme.typography.fontSizes.xs,
                  marginTop: 16,
                  marginBottom: 16,
                  textAlign: 'center',
                  lineHeight: 1.2,
                }}
              >
                {summary}
              </Text>
            );
          },
        });
      })}
    </View>
  );
};
