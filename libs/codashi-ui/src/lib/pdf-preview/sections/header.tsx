import { Text, View } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import React from 'react';

import { type Resume } from '@awe/codashi-core';
import { objectKeys } from '@awe/core';
import { theme } from '../theme';

type Props = {
  section: NonNullable<Resume['basics']>;
};

export const PdfResumeHeader: React.FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        padding: 10,
        display: 'flex',
        flexDirection: 'row',
        gap: 6,
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {objectKeys(section).map((key) => {
        return exhaustive(key, {
          name: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.lg,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {section.name}
            </Text>
          ),
          title: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {section.title}
            </Text>
          ),
          email: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.sm,
                color: theme.colors.text.link,
              }}
            >
              {section.email}
            </Text>
          ),
          phone: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {section.phone}
            </Text>
          ),
          url: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {section.url}
            </Text>
          ),
          image: () => null,
          location: () => {
            const { location } = section;

            if (!location) {
              return null;
            }

            return (
              <Text
                key={key}
                style={{
                  fontSize: 12,
                }}
              >
                {[
                  location.address,
                  location.city,
                  location.region,
                  location.country_code,
                  location.postal_code,
                ]
                  .filter(Boolean)
                  .join(', ')}
              </Text>
            );
          },
          profiles: () => {
            const { profiles } = section;
            if (!profiles || profiles.length === 0) {
              return null;
            }

            return (
              <View key={key} style={{ marginTop: 8 }}>
                {profiles.map((profile, idx) => (
                  <Text style={{ fontSize: 12 }} key={`${key}-${idx}`}>
                    {profile.network ? `${profile.network}: ` : ''}
                    {profile.username || ''}
                    {profile.url ? ` (${profile.url})` : ''}
                  </Text>
                ))}
              </View>
            );
          },
          summary: () => (
            <Text
              key={key}
              style={{
                fontSize: theme.typography.fontSizes.xs,
                marginTop: 16,
                marginBottom: 16,
                textAlign: 'center',
                lineHeight: 1.2,
              }}
            >
              {section.summary}
            </Text>
          ),
        });
      })}
    </View>
  );
};
