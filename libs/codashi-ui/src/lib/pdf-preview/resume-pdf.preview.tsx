import { Document, Page, PDFViewer } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { objectKeys } from '@awe/core';
import { PdfResumeAwards } from './sections/awards';
import { PdfResumeCertificates } from './sections/certificates';
import { PdfResumeEducation } from './sections/education';
import { PdfResumeHeader } from './sections/header';
import { PdfResumeInterests } from './sections/interests';
import { PdfResumeLanguages } from './sections/languages';
import { PdfResumeProjects } from './sections/projects';
import { PdfResumePublications } from './sections/publications';
import { PdfResumeReferences } from './sections/references';
import { PdfResumeSkills } from './sections/skills';
import { PdfResumeVolunteer } from './sections/volunteer';
import { PdfResumeWork } from './sections/work';
import { theme } from './theme';

type Props = {
  profile: Resume;
  height?: number | string;
};

export const ResumePdfPreview: FC<Props> = ({ profile, height = '100%' }) => {
  return (
    <div style={{ width: '100%', height }}>
      <PDFViewer style={{ width: '100%', height }}>
        <Document>
          <Page
            style={{
              padding: 10,
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              fontSize: 12,
              color: theme.colors.text.primary,
            }}
          >
            {objectKeys(profile).map((key) => {
              return exhaustive(key, {
                basics: () =>
                  profile.basics && (
                    <PdfResumeHeader section={profile.basics} key={key} />
                  ),
                skills: () =>
                  profile.skills?.length && (
                    <PdfResumeSkills section={profile.skills} key={key} />
                  ),
                work: () =>
                  profile.work?.length && (
                    <PdfResumeWork work={profile.work} key={key} />
                  ),
                volunteer: () =>
                  profile.volunteer?.length && (
                    <PdfResumeVolunteer
                      volunteer={profile.volunteer}
                      key={key}
                    />
                  ),
                education: () =>
                  profile.education?.length && (
                    <PdfResumeEducation
                      education={profile.education}
                      key={key}
                    />
                  ),
                awards: () =>
                  profile.awards?.length && (
                    <PdfResumeAwards awards={profile.awards} key={key} />
                  ),
                publications: () =>
                  profile.publications?.length && (
                    <PdfResumePublications
                      publications={profile.publications}
                      key={key}
                    />
                  ),
                languages: () =>
                  profile.languages?.length && (
                    <PdfResumeLanguages
                      languages={profile.languages}
                      key={key}
                    />
                  ),
                interests: () =>
                  profile.interests?.length && (
                    <PdfResumeInterests
                      interests={profile.interests}
                      key={key}
                    />
                  ),
                references: () =>
                  profile.references?.length && (
                    <PdfResumeReferences
                      references={profile.references}
                      key={key}
                    />
                  ),
                projects: () =>
                  profile.projects?.length && (
                    <PdfResumeProjects projects={profile.projects} key={key} />
                  ),
                certificates: () =>
                  profile.certificates?.length && (
                    <PdfResumeCertificates
                      certificates={profile.certificates}
                      key={key}
                    />
                  ),
                meta: () => null,
                $schema: () => null,
              });
            })}
          </Page>
        </Document>
      </PDFViewer>
    </div>
  );
};
