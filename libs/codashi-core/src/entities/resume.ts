import { z } from 'zod';

import { Deep<PERSON>artial, isoDate<PERSON><PERSON><PERSON>, zodToDomainOrThrow } from '@awe/core';

// Custom schema for Markdown content with optional template variables
// Supports standard Markdown plus template variables like {{ varName }}
const markdownString = z
  .string()
  .describe(
    'Markdown content with optional template variables using {{ varName }} syntax'
  );

const socialProfile = z.object({
  network: z.string().nullable(),
  username: z.string().nullable(),
  url: z.string().url().nullable(),
});

// Basics schema - kept separate as it's used in other parts of the codebase
export const basicsSchema = z.object({
  // Full name
  name: z.string(),
  // Professional title or role (e.g., "Software Engineer")
  title: z.string(),
  // URL to a profile image or avatar
  image: z.string().nullable().default(null),
  // Email address
  email: z.string().email(),
  // Phone number
  phone: z.string().nullable().default(null),
  // Personal website or blog URL
  url: z.string().url().nullable().default(null),

  // Location information
  location: z
    .object({
      address: z.string().nullable(),
      postal_code: z.string().nullable(),
      city: z.string().nullable(),
      country_code: z.string().nullable(),
      region: z.string().nullable(),
    })
    .nullable()
    .default(null),

  // Array of profiles (e.g., social media, online presence)
  profiles: z.array(socialProfile).nullable().default([]),

  // Brief summary or bio - supports Markdown with template variables
  summary: markdownString.nullable().default(null),
});

// Reference schema - kept separate as it's extended in other parts of the codebase
export const referenceSchema = z.object({
  name: z.string().nullable(),
  reference: z.string().nullable(),
});

const metaSchema = z.object({
  // URL to the canonical version of this resume
  canonical: z.string().url().nullable(),
  // Schema version or resume format version
  version: z.string().nullable(),
  // When the resume was last updated
  last_modified: z.string().nullable(),
});

// Define the main resume schema - follows the JSON Resume standard format
// See: https://jsonresume.org/schema/
const jsonResume = z.object({
  // Reference to the JSON Schema that validates this document
  $schema: z.string().url().nullable().default(null),
  meta: metaSchema.nullable(),
  basics: basicsSchema.nullable().default(null),

  skills: z.array(
    z.object({
      // Primary skill name (e.g., "JavaScript", "Project Management")
      name: z.string().nullable(),
      // Proficiency level (e.g., "Beginner", "Intermediate", "Expert")
      level: z.string().nullable(),
      // Related sub-skills, libraries, or specific technologies
      // For example, for "JavaScript" might include ["React", "Node.js", "TypeScript"]
      keywords: z.array(z.string()).nullable(),
    })
  ),

  projects: z.array(
    z.object({
      name: z.string().nullable(),
      // Project description - supports Markdown with template variables
      description: markdownString.nullable(),
      // Specific accomplishments or contributions to the project
      highlights: z.array(z.string()).nullable(),
      // Technical skills, tools, and technologies used
      // These can be mapped to the skills taxonomy for matching
      keywords: z.array(z.string()).nullable(),
      start_date: isoDateParser.nullable(),
      end_date: isoDateParser.nullable(),
      url: z.string().url().nullable(),
      // Roles or positions held within the project
      roles: z.array(z.string()).nullable(),
      // Organization or company associated with the project
      entity: z.string().nullable(),
      // Project category (e.g., "Open Source", "Academic", "Commercial")
      type: z.string().nullable(),
    })
  ),

  references: z.array(referenceSchema),

  languages: z.array(
    z.object({
      language: z.string().nullable(),
      fluency: z.string().nullable(),
    })
  ),

  interests: z.array(
    z.object({
      name: z.string().nullable(),
      keywords: z.array(z.string()).nullable(),
    })
  ),

  publications: z.array(
    z.object({
      name: z.string().nullable(),
      publisher: z.string().nullable(),
      release_date: isoDateParser.nullable(),
      url: z.string().url().nullable(),
      summary: z.string().nullable(),
    })
  ),

  certificates: z.array(
    z.object({
      name: z.string().nullable(),
      date: isoDateParser.nullable(),
      url: z.string().url().nullable(),
      issuer: z.string().nullable(),
    })
  ),

  awards: z.array(
    z.object({
      title: z.string().nullable(),
      date: isoDateParser.nullable(),
      awarder: z.string().nullable(),
      summary: z.string().nullable(),
    })
  ),

  work: z.array(
    z.object({
      // Company or organization name
      name: z.string().nullable().default(null),
      location: z.string().nullable().default(null),
      // Company description or industry information
      description: markdownString.nullable().default(null),
      // Job title or role
      position: z.string().nullable().default(null),
      url: z.string().url().nullable().default(null),
      start_date: isoDateParser.nullable().default(null),
      // Null/undefined indicates current position
      end_date: isoDateParser.nullable().default(null),
      // Job summary - supports Markdown with template variables
      summary: markdownString.nullable().default(null),
      // Key accomplishments as bullet points
      highlights: z.array(z.string()).nullable().default(null),
    })
  ),

  volunteer: z.array(
    z.object({
      organization: z.string().nullable(),
      position: z.string().nullable(),
      url: z.string().url().nullable(),
      start_date: isoDateParser.nullable(),
      end_date: isoDateParser.nullable(),
      summary: z.string().nullable(),
      highlights: z.array(z.string()).nullable(),
    })
  ),

  education: z.array(
    z.object({
      institution: z.string().nullable(),
      url: z.string().url().nullable(),
      area: z.string().nullable(),
      study_type: z.string().nullable(),
      start_date: isoDateParser.nullable(),
      end_date: isoDateParser.nullable(),
      score: z.string().nullable(),
      courses: z.array(z.string()).nullable(),
    })
  ),
});

// superset of the json resume schema - our software's assumptions on what
// deserves to be done deeper
const extendedResume = jsonResume.merge(
  z.object({
    meta: metaSchema.extend({}).nullable().default(null),
    references: z.array(
      referenceSchema.extend({
        email: z.string().email().nullable(),
        phone: z.string().nullable(),
        position: z.string().nullable(),
        company: z.string().nullable(),
      })
    ),

    work: z.array(
      jsonResume.shape.work.element.and(
        z.object({
          // This allows showing career progression within a company
          positions: z
            .array(
              z.object({
                // Job title or role
                position: z.string().nullable(),
                start_date: isoDateParser.nullable(),
                // Null/undefined indicates current position
                end_date: isoDateParser.nullable(),
                // Brief description of responsibilities in this role - supports Markdown with template variables
                summary: markdownString.nullable(),
                // Key accomplishments specific to this role
                highlights: z.array(z.string()).nullable(),
                // Percentage of time allocated to this role (optional)
                time_allocation: z.number().min(0).max(100).nullable(),
              })
            )
            .nullable()
            .default(null),
        })
      )
    ),
  })
);

// for now its not going to be something we save to the DB, keeping it here in case
// we decide to build it in-memory for things like suggestions
export const profile = z.object({
  basics: basicsSchema.merge(
    z.object({
      // from the basics fields - we may want to have multiple versions of our title
      // and summary as those are important for positioning ourselves in the context
      // of a job description
      title: z.array(basicsSchema.shape.title).min(1),
      summary: z.array(basicsSchema.shape.summary).min(1),
    })
  ),

  skills: extendedResume.shape.skills.min(1),
  work: extendedResume.shape.work.default([]),
  projects: extendedResume.shape.projects.default([]),
  education: extendedResume.shape.education.default([]),
  languages: extendedResume.shape.languages.default([]),
  interests: extendedResume.shape.interests.default([]),
  references: extendedResume.shape.references.default([]),
  publications: extendedResume.shape.publications.default([]),
  certificates: extendedResume.shape.certificates.default([]),
  awards: extendedResume.shape.awards.default([]),
  volunteer: extendedResume.shape.volunteer.default([]),

  // Meta information about the profile
  meta: metaSchema.nullable().default(null),
  $schema: extendedResume.shape.$schema.nullable().default(null),
});

export const resume = z
  .object({
    // these items are re-orderable thus we represent the header as an array
    $schema: jsonResume.shape.$schema.nullable().default(null),
    meta: metaSchema.and(
      z.object({
        job_description: z.string().nullable().default(null),
        created_at: isoDateParser,
      })
    ),

    header: z.object({
      // we may add additional fields here, for example - layout, centered, left aligned
      // and so on
      items: z.array(
        z.union([
          z.object({
            name: z.literal('name'),
            value: basicsSchema.shape.name,
          }),
          z.object({
            name: z.literal('title'),
            value: basicsSchema.shape.title,
          }),
          z.object({
            name: z.literal('email'),
            value: basicsSchema.shape.email,
          }),
          z.object({
            name: z.literal('phone'),
            value: basicsSchema.shape.phone,
          }),
          z.object({
            name: z.literal('url'),
            value: basicsSchema.shape.url,
          }),
          z.object({
            name: z.literal('location'),
            value: basicsSchema.shape.location,
          }),
          z.object({
            name: z.literal('summary'),
            value: basicsSchema.shape.summary,
          }),
          z.object({
            name: z.literal('profile'),
            value: socialProfile,
          }),
        ])
      ),
    }),
  })
  .merge(
    z.object({
      sections: z.array(
        z.discriminatedUnion('name', [
          z.object({
            name: z.literal('work'),
            items: extendedResume.shape.work,
          }),
          z.object({
            name: z.literal('volunteer'),
            items: extendedResume.shape.volunteer,
          }),
          z.object({
            name: z.literal('education'),
            items: extendedResume.shape.education,
          }),
          z.object({
            name: z.literal('awards'),
            items: extendedResume.shape.awards,
          }),
          z.object({
            name: z.literal('certificates'),
            items: extendedResume.shape.certificates,
          }),
          z.object({
            name: z.literal('publications'),
            items: extendedResume.shape.publications,
          }),
          z.object({
            name: z.literal('skills'),
            items: extendedResume.shape.skills,
          }),
          z.object({
            name: z.literal('languages'),
            items: extendedResume.shape.languages,
          }),
          z.object({
            name: z.literal('interests'),
            items: extendedResume.shape.interests,
          }),
          z.object({
            name: z.literal('references'),
            items: extendedResume.shape.references,
          }),
          z.object({
            name: z.literal('projects'),
            items: extendedResume.shape.projects,
          }),
        ])
      ),
    })
  );

export type Resume = z.infer<typeof resume>;

type SectionNames = Resume['sections'][number]['name'];

export type ResumeSection<T extends SectionNames> = Extract<
  Resume['sections'][number],
  { name: T }
>;

export const toResume = {
  fromPartial: (data: DeepPartial<Resume>) => zodToDomainOrThrow(resume, data),
};

/**
 * Notes on rich text fields:
 *
 * Fields using markdownString support Markdown formatting with the following features:
 * - Standard Markdown syntax (headings, lists, links, emphasis, etc.)
 * - Template variables using {{ varName }} syntax for dynamic content
 * - Limited HTML tags for additional formatting when needed
 *
 * Example:
 * ```markdown
 * ## Work Experience at {{company}}
 *
 * During my time at {{company}}, I accomplished:
 * - Increased performance by **40%**
 * - Led a team of {{teamSize}} developers
 * - Implemented <span class="highlight">key features</span>
 * ```
 *
 */
