import { Primitive } from '@awe/core';
import { extendedResume, Resume } from '../entities/resume';
import { ProfileDraft } from './draft.schema';

const CONFIDENCE_THRESHOLD = 0.65;

const PLACEHOLDERS = {
  name: 'Name not provided',
  label: 'Title not provided',
  email: 'Email not provided',
  phone: 'Phone not provided',
  url: 'https://example.com',
  summary: 'Summary not provided',
  workCompany: 'Unknown Company',
  workPosition: 'Unknown Position',
  workSummary: 'No summary provided.',
  skillName: 'Unnamed Skill',
  network: 'Unknown Network',
  username: 'Unknown Username',
  skillLevel: 'Unknown Level',
  startDate: '2020-01-01',
  endDate: '2020-12-31',
  city: 'Unknown City',
  postalCode: 'Unknown Postal Code',
  address: 'Unknown Address',
  countryCode: 'Unknown Country Code',
  region: 'Unknown Region',
  location: 'Unknown Location',
  workDescription: 'No description provided.',
  educationInstitution: 'Unknown Institution',
  educationArea: 'Unknown Field',
  educationStudyType: 'Unknown Degree',
  educationScore: 'Unknown Score',
  awardTitle: 'Unknown Award',
  awardDate: 'Unknown Date',
  awarder: 'Unknown Awarder',
  awardSummary: 'No summary provided.',
  language: 'Unknown Language',
  fluency: 'Unknown Fluency',
  projectName: 'Unnamed Project',
  projectDescription: 'No description provided.',
  projectEntity: 'Unknown Organization',
  projectType: 'Unknown Type',
  certificateName: 'Unknown Certificate',
  certificateDate: 'Unknown Date',
  certificateIssuer: 'Unknown Issuer',
  referenceName: 'Unknown Reference',
  referenceText: 'No reference provided.',
  interestName: 'Unknown Interest',
  publicationName: 'Untitled Publication',
  publicationPublisher: 'Unknown Publisher',
  publicationDate: 'Unknown Date',
  publicationSummary: 'No summary provided.',
  volunteerOrganization: 'Unknown Organization',
  volunteerPosition: 'Unknown Position',
  volunteerSummary: 'No summary provided.',
} as const;

export const draftToResume = (draft: ProfileDraft): Resume => {
  // Location: extract .value and map nulls to undefined for Resume type
  const bestLocationEntry =
    draft.location && draft.location.length > 0
      ? draft.location.reduce((a, b) =>
          a.ai_confidence > b.ai_confidence ? a : b
        )
      : undefined;

  const location =
    bestLocationEntry && bestLocationEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? {
          address: bestLocationEntry.value.address ?? PLACEHOLDERS.address,
          postal_code:
            bestLocationEntry.value.postal_code ?? PLACEHOLDERS.postalCode,
          city: bestLocationEntry.value.city ?? PLACEHOLDERS.city,
          country_code:
            bestLocationEntry.value.country_code ?? PLACEHOLDERS.countryCode,
          region: bestLocationEntry.value.region ?? PLACEHOLDERS.region,
        }
      : null;

  const work =
    draft.work.map((w) => ({
      name: w.value.name ?? PLACEHOLDERS.workCompany,
      position: w.value.position ?? PLACEHOLDERS.workPosition,
      url: w.value.url ?? PLACEHOLDERS.url,
      start_date: w.value.start_date?.toISOString() ?? PLACEHOLDERS.startDate,
      end_date: w.value.end_date?.toISOString() ?? PLACEHOLDERS.endDate,
      summary: w.value.summary ?? PLACEHOLDERS.workSummary,
      highlights: w.value.highlights?.length ? w.value.highlights : [],
      location: w.value.location ?? PLACEHOLDERS.location,
      description: w.value.description ?? PLACEHOLDERS.workDescription,
      positions: [],
    })) ?? [];

  const skills = draft.skills
    .filter((s) => s.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((s) => ({
      name: s.value.name ?? PLACEHOLDERS.skillName,
      level: s.value.level ?? PLACEHOLDERS.skillLevel,
      keywords: s.value.keywords?.length ? s.value.keywords : [],
    }));

  const education = draft.education
    .filter((e) => e.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((e) => ({
      institution: e.value.institution ?? PLACEHOLDERS.educationInstitution,
      area: e.value.area ?? PLACEHOLDERS.educationArea,
      study_type: e.value.study_type ?? PLACEHOLDERS.educationStudyType,
      url: e.value.url ?? PLACEHOLDERS.url,
      start_date:
        e.value.start_date instanceof Date
          ? e.value.start_date.toISOString()
          : e.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        e.value.end_date instanceof Date
          ? e.value.end_date.toISOString()
          : e.value.end_date ?? PLACEHOLDERS.endDate,
      score: e.value.score ?? PLACEHOLDERS.educationScore,
      courses: e.value.courses?.length ? e.value.courses : [],
    }));

  const awards = draft.awards
    .filter((a) => a.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((a) => ({
      title: a.value.title ?? PLACEHOLDERS.awardTitle,
      date:
        a.value.date instanceof Date
          ? a.value.date.toISOString()
          : a.value.date ?? PLACEHOLDERS.awardDate,
      awarder: a.value.awarder ?? PLACEHOLDERS.awarder,
      summary: a.value.summary ?? PLACEHOLDERS.awardSummary,
    }));

  const languages = draft.languages
    .filter((l) => l.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((l) => ({
      language: l.value.language ?? PLACEHOLDERS.language,
      fluency: l.value.fluency ?? PLACEHOLDERS.fluency,
    }));

  const projects = draft.projects
    .filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((p) => ({
      name: p.value.name ?? PLACEHOLDERS.projectName,
      description: p.value.description ?? PLACEHOLDERS.projectDescription,
      highlights: p.value.highlights?.length ? p.value.highlights : [],
      keywords: p.value.keywords?.length ? p.value.keywords : [],
      start_date:
        p.value.start_date instanceof Date
          ? p.value.start_date.toISOString()
          : p.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        p.value.end_date instanceof Date
          ? p.value.end_date.toISOString()
          : p.value.end_date ?? PLACEHOLDERS.endDate,
      url: p.value.url ?? PLACEHOLDERS.url,
      roles: p.value.roles?.length ? p.value.roles : [],
      entity: p.value.entity ?? PLACEHOLDERS.projectEntity,
      type: p.value.type ?? PLACEHOLDERS.projectType,
    }));

  const certificates = draft.certificates
    .filter((c) => c.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((c) => ({
      name: c.value.name ?? PLACEHOLDERS.certificateName,
      date:
        c.value.date instanceof Date
          ? c.value.date.toISOString()
          : c.value.date ?? PLACEHOLDERS.certificateDate,
      url: c.value.url ?? PLACEHOLDERS.url,
      issuer: c.value.issuer ?? PLACEHOLDERS.certificateIssuer,
    }));

  const references = draft.references
    .filter((r) => r.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((r) => ({
      name: r.value.name ?? PLACEHOLDERS.referenceName,
      reference: r.value.reference ?? PLACEHOLDERS.referenceText,
      email: r.value.email ?? PLACEHOLDERS.email,
      phone: r.value.phone ?? PLACEHOLDERS.phone,
      position: r.value.position ?? PLACEHOLDERS.workPosition,
      company: r.value.company ?? PLACEHOLDERS.workCompany,
    }));

  const interests = draft.interests
    .filter((i) => i.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((i) => ({
      name: i.value.name ?? PLACEHOLDERS.interestName,
      keywords: i.value.keywords?.length ? i.value.keywords : [],
    }));

  const publications = draft.publications
    .filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((p) => ({
      name: p.value.name ?? PLACEHOLDERS.publicationName,
      publisher: p.value.publisher ?? PLACEHOLDERS.publicationPublisher,
      release_date:
        p.value.release_date instanceof Date
          ? p.value.release_date.toISOString()
          : p.value.release_date ?? PLACEHOLDERS.publicationDate,
      url: p.value.url ?? PLACEHOLDERS.url,
      summary: p.value.summary ?? PLACEHOLDERS.publicationSummary,
    }));

  const volunteer = draft.volunteer
    .filter((v) => v.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((v) => ({
      organization: v.value.organization ?? PLACEHOLDERS.volunteerOrganization,
      position: v.value.position ?? PLACEHOLDERS.volunteerPosition,
      url: v.value.url ?? PLACEHOLDERS.url,
      start_date:
        v.value.start_date instanceof Date
          ? v.value.start_date.toISOString()
          : v.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        v.value.end_date instanceof Date
          ? v.value.end_date.toISOString()
          : v.value.end_date ?? PLACEHOLDERS.endDate,
      summary: v.value.summary ?? PLACEHOLDERS.volunteerSummary,
      highlights: v.value.highlights?.length ? v.value.highlights : [],
    }));

  return extendedResume.parse({
    basics: {
      name: pickBestByConfidence(draft.name, PLACEHOLDERS.name),
      title: pickBestByConfidence(draft.title, PLACEHOLDERS.label),
      email: pickBestByConfidence(draft.email, PLACEHOLDERS.email),
      phone: pickBestByConfidence(draft.phone, PLACEHOLDERS.phone),
      url: pickBestByConfidence(draft.url, PLACEHOLDERS.url),
      summary: pickBestByConfidence(draft.summary, PLACEHOLDERS.summary),
      location,
      image: null,
      profiles:
        draft.profiles
          ?.filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
          .map((p) => ({
            url: p.value.url ?? PLACEHOLDERS.url,
            network: p.value.network ?? PLACEHOLDERS.network,
            username: p.value.username ?? PLACEHOLDERS.username,
          })) ?? [],
    },
    work,
    skills,
    education,
    awards,
    languages,
    projects,
    certificates,
    references,
    interests,
    publications,
    volunteer,
    $schema: null,
    meta: null,
  } satisfies Primitive<Resume>);
};

const pickBestByConfidence = <
  T extends { value: V; ai_confidence: number },
  V,
  P extends V
>(
  arr: T[] | undefined,
  placeholder: P,
  minConfidence = CONFIDENCE_THRESHOLD
): V => {
  if (!arr || arr.length === 0) return placeholder;

  const best = arr.reduce((a, b) =>
    a.ai_confidence > b.ai_confidence ? a : b
  );

  return best.ai_confidence >= minConfidence ? best.value : placeholder;
};
